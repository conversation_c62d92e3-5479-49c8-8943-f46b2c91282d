import {
  Tr,
  Td,
  Text,
  Icon,
  HStack,
  Flex,
  Button,
  useToken,
  useMediaQuery,
} from '@chakra-ui/react';
import React from 'react';
import { FiCheckCircle, FiChevronUp } from 'react-icons/fi';
import { CellMeasurer, CellMeasurerCache } from 'react-virtualized';

import { DecimalMask } from 'helpers/format/fieldsMasks';

import { usePadronizacaoContext } from 'store/Padronizacao/Padronizacao';

import { ProdutoOptionProps } from 'pages/EntradaMercadoria/EntradaManual/LancamentoProdutos/ModalAdicionarProduto/validationForm';

import { ActionsMenu } from 'components/update/Table/ActionsMenu';

import {
  Produto,
  EntradaMercadoriaStatusVinculoProduto,
} from '../hooks/useProdutosVinculacao';
import { TextoTooltip } from '../TextoTooltip';

import { DetalhesItemProduto } from './DetalhesItemProduto';

interface ItemProdutoProps {
  produto: Produto;
  index: number;
  cache: CellMeasurerCache;
  style: any;
  parent: any;
  produtosTableHeaders: any[];
  onToggle: (index: number) => void;
  onEditar: (index: number) => void;
  onVincular: (
    index: number,
    produtoPendenteVariacoes?: ProdutoOptionProps
  ) => void;
  getDynamicHeight: (index: number, marginSize: number) => number;
}

export function ItemProduto({
  produto,
  index,
  cache,
  style: { height, ...restStyle },
  parent,
  produtosTableHeaders,
  onToggle,
  onEditar,
  onVincular,
  getDynamicHeight,
}: ItemProdutoProps) {
  const [teal600, aquamarine100] = useToken('colors', [
    'teal.600',
    'aquamarine.100',
  ]);
  const { casasDecimais } = usePadronizacaoContext();
  const [isLargerThan900] = useMediaQuery('(min-width: 900px)');

  const produtoEstaVinculado =
    produto.statusVinculo === EntradaMercadoriaStatusVinculoProduto.VINCULADO;

  const produtoNaoEstaVinculado =
    produto.statusVinculo ===
    EntradaMercadoriaStatusVinculoProduto.NAO_VINCULADO;

  const statusQuePodemMostrarDetalhes = [
    EntradaMercadoriaStatusVinculoProduto.VINCULADO,
    EntradaMercadoriaStatusVinculoProduto.PENDENTE_INFORMAR_VARIACOES,
  ];

  const podeMostrarDetalhes =
    statusQuePodemMostrarDetalhes.includes(produto.statusVinculo) ||
    !!produto.dadosAdicionais;

  const obterCorBackground = (
    status: EntradaMercadoriaStatusVinculoProduto
  ) => {
    const enumStatus = EntradaMercadoriaStatusVinculoProduto;
    const vinculado = status === enumStatus.VINCULADO;
    const naoVinculado = status === enumStatus.NAO_VINCULADO;

    if (vinculado) return `${teal600} !important`;
    if (naoVinculado) return 'white';
    return `${aquamarine100} !important`;
  };

  const handleVincularClick = () => {
    if (produtoNaoEstaVinculado) {
      onVincular(index);
      return;
    }

    onVincular(index, {
      id: produto.produtoVinculado?.id || '',
      nome: produto.produtoVinculado?.nome || '',
      tipoProduto: produto.produtoVinculado?.tipoProduto || 2,
      volumeUnitario: produto.produtoVinculado?.volumeUnitario || false,
      referencia: produto.produtoVinculado?.referencia || '',
      precoCompra: produto.produtoVinculado?.precoCompra || 0,
      coresOptions: [],
      tamanhosOptions: [],
    });
  };

  return (
    <CellMeasurer
      cache={cache}
      columnIndex={1}
      key={`produto-${index}`}
      parent={parent}
      rowIndex={index}
    >
      {({ registerChild, measure }) => (
        <>
          <Tr
            transition="all 0.3s"
            ref={(e) => {
              if (e && registerChild) {
                registerChild(e);
              }
            }}
            style={restStyle}
            h={`${getDynamicHeight(index, 0)}px !important`}
            bg={obterCorBackground(produto.statusVinculo)}
            sx={{
              '& > td': {
                bg: obterCorBackground(produto.statusVinculo),
                color: produtoEstaVinculado ? 'white' : 'inherit',
                ...(produto.isOpen
                  ? {
                      marginBottom: '5px',
                      borderBottomRadius: '0px !important',
                    }
                  : {}),
              },
            }}
          >
            <Td
              width={produtosTableHeaders[0].width}
              cursor={podeMostrarDetalhes ? 'pointer' : 'default'}
              userSelect="none"
              fontSize="14px"
              onClick={() => {
                measure();
                if (podeMostrarDetalhes) onToggle(index);
              }}
            >
              <Button
                tabIndex={0}
                bg="transparent"
                p="4px"
                pb="0px"
                mr="6px"
                h="fit-content"
                borderRadius="6px"
                _focus={{
                  background: 'gray.100',
                }}
                minW="16px"
                opacity={podeMostrarDetalhes ? '1' : '0'}
                pointerEvents={podeMostrarDetalhes ? 'all' : 'none'}
              >
                <Icon
                  as={FiChevronUp}
                  mb="6px"
                  transform={produto.isOpen ? '' : 'rotate(180deg)'}
                  role="button"
                  transition="all 0.3s"
                />
              </Button>
              {produto.descricaoProdutoNota}
              {produto.isOpen && produto.dadosAdicionais && (
                <Flex
                  position="absolute"
                  w="97%"
                  flexDir="row"
                  align="center"
                  pl="30px"
                  mt="4px"
                  gap="4px"
                  fontSize="12px"
                  fontWeight="bold"
                >
                  <TextoTooltip
                    texto={produto.dadosAdicionais}
                    maxWidth="100%"
                  />
                </Flex>
              )}
            </Td>
            <Td
              width={produtosTableHeaders[1].width}
              minWidth={produtosTableHeaders[1].width}
              fontSize="14px"
              textAlign="center"
            >
              {DecimalMask(
                produto.quantidade,
                casasDecimais.casasDecimaisQuantidade
              )}
            </Td>
            <Td
              width={produtosTableHeaders[2].width}
              minWidth={produtosTableHeaders[2].width}
              isNumeric
              fontSize="14px"
              textAlign="right"
            >
              {DecimalMask(
                produto.valorUnitario,
                casasDecimais.casasDecimaisValor
              )}
            </Td>
            <Td
              width={produtosTableHeaders[3].width}
              minWidth={produtosTableHeaders[3].width}
              isNumeric
              fontSize="14px"
              textAlign="right"
            >
              {DecimalMask(produto.valorTotal, 2, 2)}
            </Td>
            <Td
              width={produtosTableHeaders[4].width}
              minWidth={produtosTableHeaders[4].width}
            >
              {produtoEstaVinculado ? (
                <Flex justifyContent="space-between">
                  <HStack spacing="1" color="secondary.300">
                    <Icon as={FiCheckCircle} boxSize="4" />
                    <Text fontSize="xs">Vinculado</Text>
                  </HStack>
                  <ActionsMenu
                    colorScheme="white"
                    backgroundHoverColor="gray.500"
                    items={[
                      {
                        content: 'Editar',
                        onClick: () => onEditar(index),
                      },
                    ]}
                  />
                </Flex>
              ) : (
                <Flex alignItems="center" justifyContent="center">
                  <Button
                    size="xs"
                    colorScheme="orange"
                    minW={isLargerThan900 ? '136px' : '96px'}
                    onClick={handleVincularClick}
                  >
                    {produtoNaoEstaVinculado
                      ? `Vincular${isLargerThan900 ? ' ao sistema' : ''}`
                      : 'Informar variações'}
                  </Button>
                </Flex>
              )}
            </Td>
            {produto.isOpen && (
              <DetalhesItemProduto
                produto={produto}
                obterCorBackground={obterCorBackground}
              />
            )}
          </Tr>
        </>
      )}
    </CellMeasurer>
  );
}
