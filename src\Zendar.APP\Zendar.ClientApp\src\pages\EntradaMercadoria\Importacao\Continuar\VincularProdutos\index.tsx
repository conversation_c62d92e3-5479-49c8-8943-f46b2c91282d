import { But<PERSON>, <PERSON>ack, useMediaQuery } from '@chakra-ui/react';
import { toast } from 'react-toastify';

import { useEntradaMercadoriaDadosCadastroContext } from 'store/EntradaMercadoria/EntradaMercadoriaDadosCadastro';
import { useEntradaMercadoriaEtapasContext } from 'store/EntradaMercadoria/EntradaMercadoriaEtapas';

import {
  Container,
  Body,
  Footer,
  StepDescriptionAccordion,
} from 'components/update/Steps/StepContent';

import { TanStackListagemProdutos } from './components/TanStackListagemProdutos';
import TotalizadoresFixos from './components/TotalizadoresFixos';
import { useProdutosVinculacao } from './hooks/useProdutosVinculacao';

export function VincularProdutos() {
  const [isLargerThan900] = useMediaQuery('(min-width: 900px)');

  const { nextStep, previousStep } = useEntradaMercadoriaEtapasContext();
  const {
    entradaMercadoriaId,
    descartarEntradaMercadoria,
    voltarParaListagem,
    temPermissaoExcluir,
    isReadOnly,
    menuIsOpen,
  } = useEntradaMercadoriaDadosCadastroContext();

  const {
    produtos,
    todosProdutosVinculados,
    isLoading,
    informacoesRodape,
    handleToggleLinhaProduto,
    handleEditar,
    handleVincularProduto,
    loadMoreRows,
  } = useProdutosVinculacao(entradaMercadoriaId ?? null);

  function handleDescartarEntradaMercadoria() {
    descartarEntradaMercadoria();
  }

  function handleVoltar() {
    previousStep();
  }

  function handleSalvarRascunho() {
    voltarParaListagem();
  }

  function handleAvancar() {
    if (todosProdutosVinculados) {
      nextStep();
    } else {
      toast.warning('É necessário vincular todos os produtos para continuar.');
    }
  }

  return (
    <>
      <Container mt="6px">
        <StepDescriptionAccordion
          stepNumber={2}
          title="Lista de produtos"
          description='Todos os produtos contidos na nota fiscal precisam ser vinculados a um produto existente no sistema. Clique em "vincular ao sistema" em cada um dos itens listados abaixo para realizar esta ação. Caso exista um novo produto você poderá cadastrá-lo na própria tela de vinculação.'
        />
        <Body>
          <TanStackListagemProdutos
            produtos={produtos}
            informacoesRodape={informacoesRodape}
            isLoading={isLoading}
            handleToggleLinhaProduto={handleToggleLinhaProduto}
            handleEditar={handleEditar}
            handleVincularProduto={handleVincularProduto}
          />
        </Body>
      </Container>
      {informacoesRodape.totalProdutos > 0 && (
        <TotalizadoresFixos
          quantidadeItens={informacoesRodape.quantidadeItens}
          totalProdutos={informacoesRodape.totalProdutos}
          valorTotalProdutos={informacoesRodape.valorTotalProdutos}
        />
      )}
      <Footer
        justifyContent="space-between"
        position={isLargerThan900 ? 'fixed' : 'relative'}
        bottom="0px"
        bg="gray.50"
        borderTop={isLargerThan900 ? '1px solid' : 'none'}
        borderColor="#5502B2"
        w={`calc(100% - ${menuIsOpen ? '210px' : '108px'})`}
        py="16px"
        px="48px"
      >
        <Button
          variant="outlineDefault"
          borderRadius="full"
          w="full"
          maxW={{ base: 'full', md: '160px' }}
          onClick={handleVoltar}
        >
          Voltar
        </Button>
        <Stack
          w="full"
          justifyContent="flex-end"
          direction={{ base: 'column', md: 'row' }}
          spacing={{ base: 2, sm: 4, md: 6 }}
        >
          {isReadOnly ? (
            <Button
              variant="outlineDefault"
              borderRadius="full"
              w="full"
              maxW={{ base: 'full', md: '196px' }}
              onClick={voltarParaListagem}
            >
              Voltar para a listagem
            </Button>
          ) : (
            <Button
              variant="outlineDefault"
              borderRadius="full"
              w="full"
              maxW={{ base: 'full', md: '160px' }}
              onClick={handleDescartarEntradaMercadoria}
              isDisabled={!temPermissaoExcluir}
            >
              Descartar
            </Button>
          )}
          {!isReadOnly && (
            <Button
              variant="outlineDefault"
              borderRadius="full"
              w="full"
              maxW={{ base: 'full', md: '160px' }}
              onClick={handleSalvarRascunho}
            >
              Salvar e sair
            </Button>
          )}
          <Button
            colorScheme="purple"
            borderRadius="full"
            w="full"
            maxW={{ base: 'full', md: '160px' }}
            onClick={handleAvancar}
            isDisabled={!todosProdutosVinculados}
          >
            Avançar
          </Button>
        </Stack>
      </Footer>
    </>
  );
}
